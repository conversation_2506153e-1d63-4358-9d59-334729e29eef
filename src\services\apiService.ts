// API service for handling all API calls related to dealer checklist submissions

// API endpoint for fetching dealer checklist submissions
const API_ENDPOINT = 'https://api.eisqr.com/dealer-checklist-submissions/latest-by-vendor';

/**
 * Fetch raw data from the API
 * @returns Promise<any[]> - Array of raw API data objects
 */
export async function fetchDealerChecklistData(): Promise<any[]> {
  try {
    const response = await fetch(API_ENDPOINT);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // Parse the stringified response field for each object
    return data.map((item: any) => ({
      ...item,
      response: typeof item.response === 'string' ? JSON.parse(item.response) : item.response
    }));
  } catch (error) {
    console.error('Error fetching dealer checklist data:', error);
    throw error;
  }
}

/**
 * Extract filter options from raw API data
 * @param rawData - Raw API data array
 * @returns Filter options object
 */
export function extractFilterOptions(rawData: any[]) {
  const zones = new Set<string>();
  const locations = new Set<string>();
  const dealers = new Set<string>();
  const areaOffices = new Set<string>();
  const categories = new Set<string>();

  // Helper function to get zone name from zone code
  function getZoneName(zoneCode: string): string {
    const zoneMap: Record<string, string> = {
      'N': 'North',
      'S': 'South',
      'E': 'East',
      'W': 'West',
      'C': 'Central'
    };
    return zoneMap[zoneCode] || zoneCode;
  }

  // Helper function to get category name from category code
  function getCategoryName(categoryCode: string): string {
    const categoryMap: Record<string, string> = {
      'A': 'Category A',
      'B': 'Category B',
      'C': 'Category C',
      'D': 'Category D'
    };
    return categoryMap[categoryCode] || categoryCode;
  }

  console.log('Extracting filter options from API data:', rawData.length, 'objects');

  rawData.forEach(dataObject => {
    if (dataObject.vendor) {
      const vendor = dataObject.vendor;
      
      if (vendor.dealerZone) zones.add(getZoneName(vendor.dealerZone));
      if (vendor.dealerLocation) locations.add(vendor.dealerLocation);
      if (vendor.dealerName) dealers.add(vendor.dealerName);
      if (vendor.dealerAO) areaOffices.add(vendor.dealerAO);
      if (vendor.dealerCategory) categories.add(getCategoryName(vendor.dealerCategory));
    }
  });

  const filterOptions = {
    zones: ['All', ...Array.from(zones).sort()],
    locations: ['All', ...Array.from(locations).sort()],
    dealers: ['All', ...Array.from(dealers).sort()],
    areaOffices: ['All', ...Array.from(areaOffices).sort()],
    categories: ['All', ...Array.from(categories).sort()]
  };

  console.log('Filter options extracted:', filterOptions);
  return filterOptions;
}

/**
 * Get filtered options based on current filter selections
 * @param rawData - Raw API data array
 * @param currentFilters - Current filter selections
 * @returns Filtered options object
 */
export function getFilteredOptions(rawData: any[], currentFilters: {
  zone?: string;
  location?: string;
  dealer?: string;
  category?: string;
}) {
  const locations = new Set<string>();
  const dealers = new Set<string>();
  const areaOffices = new Set<string>();
  const categories = new Set<string>();

  // Helper functions (same as above)
  function getZoneName(zoneCode: string): string {
    const zoneMap: Record<string, string> = {
      'N': 'North', 'S': 'South', 'E': 'East', 'W': 'West', 'C': 'Central'
    };
    return zoneMap[zoneCode] || zoneCode;
  }

  function getCategoryName(categoryCode: string): string {
    const categoryMap: Record<string, string> = {
      'A': 'Category A', 'B': 'Category B', 'C': 'Category C', 'D': 'Category D'
    };
    return categoryMap[categoryCode] || categoryCode;
  }

  console.log('Filtering options with current filters:', currentFilters);

  // Filter data based on current selections
  rawData.forEach(dataObject => {
    if (!dataObject.vendor) return;

    const vendor = dataObject.vendor;
    const vendorDetails = {
      zone: getZoneName(vendor.dealerZone),
      city: vendor.dealerLocation,
      dealerName: vendor.dealerName,
      areaOffice: vendor.dealerAO,
      category: getCategoryName(vendor.dealerCategory)
    };

    // Apply zone filter
    if (currentFilters.zone && currentFilters.zone !== 'All') {
      if (vendorDetails.zone !== currentFilters.zone) return;
    }

    // Apply location filter
    if (currentFilters.location && currentFilters.location !== 'All') {
      if (vendorDetails.city !== currentFilters.location) return;
    }

    // Apply dealer filter
    if (currentFilters.dealer && currentFilters.dealer !== 'All') {
      if (vendorDetails.dealerName !== currentFilters.dealer) return;
    }

    // Apply category filter
    if (currentFilters.category && currentFilters.category !== 'All') {
      if (vendorDetails.category !== currentFilters.category) return;
    }

    // Add to available options if passes all filters
    if (vendorDetails.city) locations.add(vendorDetails.city);
    if (vendorDetails.dealerName) dealers.add(vendorDetails.dealerName);
    if (vendorDetails.areaOffice) areaOffices.add(vendorDetails.areaOffice);
    if (vendorDetails.category) categories.add(vendorDetails.category);
  });

  const filteredOptions = {
    locations: ['All', ...Array.from(locations).sort()],
    dealers: ['All', ...Array.from(dealers).sort()],
    areaOffices: ['All', ...Array.from(areaOffices).sort()],
    categories: ['All', ...Array.from(categories).sort()]
  };

  console.log('Filtered options result:', filteredOptions);
  return filteredOptions;
}
