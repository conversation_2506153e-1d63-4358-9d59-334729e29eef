import msiData from '../data/msiData.json';

interface QuestionSummary {
  id: string;
  question: string;
  category: string;
  subCategory: string;
  response: string;
  salesScore: number | null;
  serviceScore: number | null;
  maxScore: number;
  status: 'Excellent' | 'Good' | 'Needs Improvement' | 'Critical';
}

interface CategorySummary {
  categoryName: string;
  totalQuestions: number;
  excellentCount: number;
  goodCount: number;
  needsImprovementCount: number;
  criticalCount: number;
  questions: QuestionSummary[];
}

// Function to determine status based on score percentage
function getQuestionStatus(actualScore: number | null, maxScore: number): 'Excellent' | 'Good' | 'Needs Improvement' | 'Critical' {
  if (actualScore === null || maxScore === 0) return 'Critical';
  
  const percentage = (actualScore / maxScore) * 100;
  
  if (percentage >= 90) return 'Excellent';
  if (percentage >= 70) return 'Good';
  if (percentage >= 50) return 'Needs Improvement';
  return 'Critical';
}

// Function to get all question responses summarized
export function getAllQuestionResponses(): CategorySummary[] {
  const categorySummaries: CategorySummary[] = [];

  msiData.categories.forEach(category => {
    const categoryQuestions: QuestionSummary[] = [];
    
    category.criteria.forEach(criteria => {
      if (criteria.subCriteriaDetails) {
        criteria.subCriteriaDetails.forEach(subCriteria => {
          subCriteria.questions.forEach(question => {
            const salesScore = question.salesScore;
            const serviceScore = question.serviceScore;
            const actualScore = question.actualScore || salesScore || serviceScore;
            
            const questionSummary: QuestionSummary = {
              id: question.id,
              question: question.question,
              category: criteria.mainCriteria,
              subCategory: subCriteria.name,
              response: question.response || 'No response provided',
              salesScore: salesScore,
              serviceScore: serviceScore,
              maxScore: question.maxScore,
              status: getQuestionStatus(actualScore, question.maxScore)
            };
            
            categoryQuestions.push(questionSummary);
          });
        });
      }
    });

    // Count status distribution
    const excellentCount = categoryQuestions.filter(q => q.status === 'Excellent').length;
    const goodCount = categoryQuestions.filter(q => q.status === 'Good').length;
    const needsImprovementCount = categoryQuestions.filter(q => q.status === 'Needs Improvement').length;
    const criticalCount = categoryQuestions.filter(q => q.status === 'Critical').length;

    categorySummaries.push({
      categoryName: category.name,
      totalQuestions: categoryQuestions.length,
      excellentCount,
      goodCount,
      needsImprovementCount,
      criticalCount,
      questions: categoryQuestions
    });
  });

  return categorySummaries;
}

// Function to get responses by category
export function getResponsesByCategory(categoryName: string): QuestionSummary[] {
  const allResponses = getAllQuestionResponses();
  const category = allResponses.find(cat => cat.categoryName === categoryName);
  return category ? category.questions : [];
}

// Function to get responses by status
export function getResponsesByStatus(status: 'Excellent' | 'Good' | 'Needs Improvement' | 'Critical'): QuestionSummary[] {
  const allResponses = getAllQuestionResponses();
  const allQuestions: QuestionSummary[] = [];
  
  allResponses.forEach(category => {
    allQuestions.push(...category.questions);
  });
  
  return allQuestions.filter(question => question.status === status);
}

// Function to get summary statistics
export function getSummaryStatistics() {
  const allResponses = getAllQuestionResponses();
  
  let totalQuestions = 0;
  let totalExcellent = 0;
  let totalGood = 0;
  let totalNeedsImprovement = 0;
  let totalCritical = 0;
  
  allResponses.forEach(category => {
    totalQuestions += category.totalQuestions;
    totalExcellent += category.excellentCount;
    totalGood += category.goodCount;
    totalNeedsImprovement += category.needsImprovementCount;
    totalCritical += category.criticalCount;
  });
  
  return {
    totalQuestions,
    totalExcellent,
    totalGood,
    totalNeedsImprovement,
    totalCritical,
    excellentPercentage: Math.round((totalExcellent / totalQuestions) * 100),
    goodPercentage: Math.round((totalGood / totalQuestions) * 100),
    needsImprovementPercentage: Math.round((totalNeedsImprovement / totalQuestions) * 100),
    criticalPercentage: Math.round((totalCritical / totalQuestions) * 100),
    categories: allResponses
  };
}

// Function to export responses as formatted text
export function exportResponsesSummary(): string {
  const allResponses = getAllQuestionResponses();
  let summary = "QUESTION RESPONSES SUMMARY\n";
  summary += "=" + "=".repeat(50) + "\n\n";
  
  allResponses.forEach(category => {
    summary += `CATEGORY: ${category.categoryName.toUpperCase()}\n`;
    summary += "-".repeat(40) + "\n";
    summary += `Total Questions: ${category.totalQuestions}\n`;
    summary += `Excellent: ${category.excellentCount} | Good: ${category.goodCount} | Needs Improvement: ${category.needsImprovementCount} | Critical: ${category.criticalCount}\n\n`;
    
    category.questions.forEach(question => {
      summary += `[${question.id}] ${question.status.toUpperCase()}\n`;
      summary += `Question: ${question.question}\n`;
      summary += `Sub-Category: ${question.subCategory}\n`;
      summary += `Response: ${question.response}\n`;
      summary += `Score: Sales(${question.salesScore || 'N/A'}) | Service(${question.serviceScore || 'N/A'}) | Max(${question.maxScore})\n`;
      summary += "\n";
    });
    
    summary += "\n";
  });
  
  return summary;
}
