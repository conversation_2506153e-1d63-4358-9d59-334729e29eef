import React from "react";
import { Di<PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";

interface QuestionDetailPopupProps {
  isOpen: boolean;
  onClose: () => void;
  question: any;
  subCriteriaName: string;
}

const QuestionDetailPopup: React.FC<QuestionDetailPopupProps> = ({
  isOpen,
  onClose,
  question,
  subCriteriaName,
}) => {
  if (!question) return null;

  /** Color grading based on percentage */
  const getColor = (percentage: number) => {
    if (percentage >= 90) return "bg-green-500 text-white";
    if (percentage >= 70) return "bg-lime-400 text-white";
    if (percentage >= 50) return "bg-yellow-400 text-gray-900";
    if (percentage >= 30) return "bg-orange-400 text-white";
    return "bg-red-500 text-white";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold">
            {subCriteriaName} - {question.id}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <p className="font-semibold text-gray-800 mb-2">{question.question}</p>
            <p className="text-sm text-gray-600">
              Score: <span className="font-bold">{question.actualScore}</span> /{" "}
              {question.maxScore}
            </p>
            <p className="text-sm text-gray-600">
              Response: <span className="font-bold">{question.response}</span>
            </p>
          </div>

          {/* Response percentages section */}
          {question.responsePercentages && (
            <div className="space-y-2">
              <p className="font-semibold text-gray-800">Response Distribution</p>
              <div className="grid grid-cols-3 gap-2">
                {Object.keys(question.responsePercentages).map((label) => {
                  const percentage = question.responsePercentages[label];
                  const count = question.responseCounts[label] || 0;
                  return (
                    <div
                      key={label}
                      className={`rounded p-2 text-center text-xs font-semibold ${getColor(
                        percentage
                      )}`}
                    >
                      <div>{label}</div>
                      <div>{percentage}%</div>
                      <div className="text-[10px] opacity-80">({count})</div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QuestionDetailPopup;
