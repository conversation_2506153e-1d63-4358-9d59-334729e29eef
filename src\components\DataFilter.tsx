import { useMemo } from 'react';

export interface FilterCriteria {
  zone?: string;
  location?: string;
  dealer?: string;
  areaOffice?: string;
  category?: string;
}

interface DataFilterProps {
  rawData: any[];
  filters: FilterCriteria;
}

// Helper mappings
const categoryList = [
  { name: 'Forging & Machining', value: 1 },
  { name: 'Casting & Machining', value: 2 },
  { name: 'Pressing & Fabrication', value: 3 },
  { name: 'Proprietary Mechanical', value: 4 },
  { name: 'Proprietary Electrical', value: 5 },
  { name: 'Plastics, Rubber, Painting and Stickers', value: 6 },
  { name: 'EV/3W/2W', value: 7 },
  { name: 'BW', value: 8 },
  { name: 'Accessories', value: 9 }
];

const zonalOfficeList = [
  { name: "Central", value: 1 },
  { name: "East", value: 2 },
  { name: "North", value: 3 },
  { name: "South", value: 9 },
  { name: "South1", value: 4 },
  { name: "South2", value: 5 },
  { name: "West", value: 8 },
  { name: "West1", value: 6 },
  { name: "West2", value: 7 }
];

const getCategoryName = (value: number): string =>
  categoryList.find(cat => cat.value === value)?.name || `Category ${value}`;

const getZoneName = (value: number): string =>
  zonalOfficeList.find(zone => zone.value === value)?.name || `Zone ${value}`;

/** 
 * HOOK: filters data only when rawData or filters change
 */
export function useDataFilter({ rawData, filters }: DataFilterProps): any[] {
  return useMemo(() => {
    if (!rawData || rawData.length === 0) return [];

    return rawData.filter(dataObject => {
      if (!dataObject.vendor) return false;

      const vendor = dataObject.vendor;
      const zoneName = getZoneName(vendor.dealerZone);
      const categoryName = getCategoryName(vendor.dealerCategory);

      if (filters.zone && filters.zone !== 'All' && zoneName !== filters.zone) return false;
      if (filters.location && filters.location !== 'All' && vendor.dealerLocation !== filters.location) return false;
      if (filters.dealer && filters.dealer !== 'All' && vendor.dealerName !== filters.dealer) return false;
      if (filters.areaOffice && filters.areaOffice !== 'All' && vendor.dealerAO !== filters.areaOffice) return false;
      if (filters.category && filters.category !== 'All' && categoryName !== filters.category) return false;

      return true;
    });
  }, [rawData, filters]);
}

/** 
 * Get filter options
 */
export function getFilterOptions(dataArray: any[]) {
  const zones = new Set<string>();
  const locations = new Set<string>();
  const dealers = new Set<string>();
  const areaOffices = new Set<string>();
  const categories = new Set<string>();

  dataArray.forEach(dataObject => {
    if (!dataObject.vendor) return;

    const vendor = dataObject.vendor;
    zones.add(getZoneName(vendor.dealerZone));
    if (vendor.dealerLocation) locations.add(vendor.dealerLocation);
    if (vendor.dealerName) dealers.add(vendor.dealerName);
    if (vendor.dealerAO) areaOffices.add(vendor.dealerAO);
    categories.add(getCategoryName(vendor.dealerCategory));
  });

  return {
    zones: ['All', ...Array.from(zones).sort()],
    locations: ['All', ...Array.from(locations).sort()],
    dealers: ['All', ...Array.from(dealers).sort()],
    areaOffices: ['All', ...Array.from(areaOffices).sort()],
    categories: ['All', ...Array.from(categories).sort()],
  };
}

/**
 * Get filtered options (dependent dropdown options)
 */
export function getFilteredOptions(dataArray: any[], filters: Partial<FilterCriteria>) {
  const locations = new Set<string>();
  const dealers = new Set<string>();
  const areaOffices = new Set<string>();
  const categories = new Set<string>();

  dataArray.forEach(dataObject => {
    if (!dataObject.vendor) return;

    const vendor = dataObject.vendor;
    const zoneName = getZoneName(vendor.dealerZone);
    const categoryName = getCategoryName(vendor.dealerCategory);

    if (filters.zone && filters.zone !== 'All' && zoneName !== filters.zone) return;
    if (filters.location && filters.location !== 'All' && vendor.dealerLocation !== filters.location) return;
    if (filters.dealer && filters.dealer !== 'All' && vendor.dealerName !== filters.dealer) return;
    if (filters.category && filters.category !== 'All' && categoryName !== filters.category) return;

    if (vendor.dealerLocation) locations.add(vendor.dealerLocation);
    if (vendor.dealerName) dealers.add(vendor.dealerName);
    if (vendor.dealerAO) areaOffices.add(vendor.dealerAO);
    categories.add(categoryName);
  });

  return {
    locations: ['All', ...Array.from(locations).sort()],
    dealers: ['All', ...Array.from(dealers).sort()],
    areaOffices: ['All', ...Array.from(areaOffices).sort()],
    categories: ['All', ...Array.from(categories).sort()],
  };
}

/** Fetch API */
export async function fetchPreApiData() {
  const API_ENDPOINT = 'https://api.eisqr.com/dealer-checklist-submissions/latest-by-vendor';
  const response = await fetch(API_ENDPOINT);
  if (!response.ok) throw new Error('API fetch failed');

  const data = await response.json();
  return data.map((item: any) => ({
    ...item,
    response: typeof item.response === 'string' ? JSON.parse(item.response) : item.response,
  }));
}
