import React, { useState, useEffect, useMemo } from 'react';
import { transformPreApiData } from '../utils/dataTransform';
import {
  useDataFilter,
  FilterCriteria,
  fetchPreApiData,
  getFilterOptions,
  getFilteredOptions,
} from './DataFilter';
import SalesHeatmap from './SalesHeatmap';
import ServiceHeatmap from './ServiceHeatmap';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const MSIHeatmap = () => {
  const [rawApiData, setRawApiData] = useState<any[]>([]);
  const [msiData, setMsiData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [globalFilter, setGlobalFilter] = useState('All');
  const [zoneFilter, setZoneFilter] = useState('All');
  const [locationFilter, setLocationFilter] = useState('All');
  const [dealerFilter, setDealerFilter] = useState('All');
  const [areaOfficeFilter, setAreaOfficeFilter] = useState('All');
  const [categoryFilter, setCategoryFilter] = useState('All');

  // All filter options
  const [allFilterOptions, setAllFilterOptions] = useState({
    zones: ['All'],
    locations: ['All'],
    dealers: ['All'],
    areaOffices: ['All'],
    categories: ['All'],
  });

  // Filtered options for dependent dropdowns
  const [filteredOptions, setFilteredOptions] = useState({
    locations: ['All'],
    dealers: ['All'],
    areaOffices: ['All'],
    categories: ['All'],
  });

  // Build filter criteria object
  const filterCriteria: FilterCriteria = {
    zone: zoneFilter,
    location: locationFilter,
    dealer: dealerFilter,
    areaOffice: areaOfficeFilter,
    category: categoryFilter,
  };

  // Memoize filtered data
  const filteredData = useDataFilter({ rawData: rawApiData, filters: filterCriteria });


  // Fetch raw API data on mount
  useEffect(() => {
    const loadRawData = async () => {
      try {
        setLoading(true);
        const data = await fetchPreApiData();
        setRawApiData(data);
        setError(null);
      } catch (err) {
        console.error('Error loading raw data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadRawData();
  }, []);

  // Transform data when filteredData or rawApiData changes
  useEffect(() => {
    const transformData = async () => {
      if (filteredData.length > 0 || rawApiData.length > 0) {
        try {
          const transformed = await transformPreApiData(
            filteredData.length > 0 ? filteredData : rawApiData
          );
          setMsiData(transformed);
        } catch (err) {
          console.error('Error transforming data:', err);
          setError('Failed to transform data');
        }
      }
    };
    transformData();
  }, [filteredData, rawApiData]);

  // Load filter options initially
  useEffect(() => {
    if (rawApiData.length > 0) {
      const options = getFilterOptions(rawApiData);
      setAllFilterOptions(options);
    }
  }, [rawApiData]);

  // Update dependent filter options
  useEffect(() => {
    if (rawApiData.length > 0) {
      const options = getFilteredOptions(rawApiData, {
        zone: zoneFilter,
        location: locationFilter,
        dealer: dealerFilter,
        category: categoryFilter,
      });
      setFilteredOptions(options);
    }
  }, [zoneFilter, locationFilter, dealerFilter, categoryFilter, rawApiData]);

  // Reset dependent filters inside handlers (removed unnecessary useEffects)
  const handleZoneChange = (newZone: string) => {
    setZoneFilter(newZone);
    setLocationFilter('All');
    setDealerFilter('All');
    setAreaOfficeFilter('All');
    setCategoryFilter('All');
  };

  const handleLocationChange = (newLocation: string) => {
    setLocationFilter(newLocation);
    setDealerFilter('All');
    setAreaOfficeFilter('All');
  };

  const handleCategoryChange = (newCategory: string) => {
    setCategoryFilter(newCategory);
    setDealerFilter('All');
    setAreaOfficeFilter('All');
  };

  const handleDealerChange = (newDealer: string) => {
    setDealerFilter(newDealer);
    setAreaOfficeFilter('All');
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading heatmap data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !msiData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <p className="text-lg text-red-600">{error || 'Failed to load data'}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { totals } = msiData || { totals: { sales: {}, service: {} } };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8 overflow-visible">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dealer MSI Calibration Score
          </h1>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-gradient-to-br from-blue-400 to-blue-500 text-white shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Sales Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totals.sales.percentage}%</div>
              <div className="text-blue-100 text-sm">
                {totals.sales.actualScore} / {totals.sales.maxScore}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-400 to-purple-500 text-white shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Service Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totals.service.percentage}%</div>
              <div className="text-purple-100 text-sm">
                {totals.service.actualScore} / {totals.service.maxScore}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-sky-300 to-sky-400 text-white shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Overall Dealership Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totals.dealershipScore}%</div>
              <div className="text-sky-100 text-sm">Combined Average</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/20 overflow-visible mb-4">
          <div className="flex flex-col lg:flex-row gap-4 justify-center items-center flex-wrap">
            {/* Zone */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Zone</h3>
              <select
                value={zoneFilter}
                onChange={(e) => handleZoneChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm"
              >
                {allFilterOptions.zones.map((zone) => (
                  <option key={zone} value={zone}>
                    {zone}
                  </option>
                ))}
              </select>
            </div>

            {/* Location */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Location</h3>
              <select
                value={locationFilter}
                onChange={(e) => handleLocationChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm"
              >
                {filteredOptions.locations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>

            {/* Category */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Category</h3>
              <select
                value={categoryFilter}
                onChange={(e) => handleCategoryChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm"
              >
                {filteredOptions.categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Dealer */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Dealer</h3>
              <select
                value={dealerFilter}
                onChange={(e) => handleDealerChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm"
              >
                {filteredOptions.dealers.map((dealer) => (
                  <option key={dealer} value={dealer}>
                    {dealer}
                  </option>
                ))}
              </select>
            </div>

            {/* Area Office */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Area Office</h3>
              <select
                value={areaOfficeFilter}
                onChange={(e) => setAreaOfficeFilter(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm"
              >
                {filteredOptions.areaOffices.map((office) => (
                  <option key={office} value={office}>
                    {office}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Heatmaps */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <SalesHeatmap
            categories={msiData.categories || []}
            globalFilter={globalFilter}
            zoneFilter={zoneFilter}
            locationFilter={locationFilter}
            categoryFilter={categoryFilter}
            dealerFilter={dealerFilter}
            areaOfficeFilter={areaOfficeFilter}
          />
          <ServiceHeatmap
            categories={msiData.categories || []}
            globalFilter={globalFilter}
            zoneFilter={zoneFilter}
            locationFilter={locationFilter}
            categoryFilter={categoryFilter}
            dealerFilter={dealerFilter}
            areaOfficeFilter={areaOfficeFilter}
          />
        </div>
      </div>
    </div>
  );
};

export default MSIHeatmap;
