
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { ChevronRight } from 'lucide-react';
import QuestionDetailPopup from './QuestionDetailPopup';

interface Question {
  id: string;
  question: string;
  maxScore: number;
  actualScore: number;
  response?: string;
}

interface SubCriteria {
  id: string;
  name: string;
  questions: Question[];
  salesQuestions?: Question[];
  serviceQuestions?: Question[];
}

interface QuestionWiseHeatmapProps {
  isOpen: boolean;
  onClose: () => void;
  criteriaName: string;
  department: 'sales' | 'service';
  subCriteria: SubCriteria[];
}



const QuestionWiseHeatmap: React.FC<QuestionWiseHeatmapProps> = ({
  isOpen,
  onClose,
  criteriaName,
  department,
  subCriteria
}) => {
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [selectedSubCriteriaName, setSelectedSubCriteriaName] = useState<string>('');

  // Debug logging
  console.log('QuestionWiseHeatmap props:', {
    isOpen,
    criteriaName,
    department,
    subCriteria
  });

  const handleQuestionClick = (question: Question, subCriteriaName: string) => {
    setSelectedQuestion(question);
    setSelectedSubCriteriaName(subCriteriaName);
  };

  const handleQuestionPopupClose = () => {
    setSelectedQuestion(null);
    setSelectedSubCriteriaName('');
  };
  const getScoreColor = (actualScore: number, maxScore: number) => {
    const percentage = (actualScore / maxScore) * 100;

    if (percentage === 100) return 'bg-green-500 text-white';
    if (percentage >= 80) return 'bg-lime-400 text-white';
    if (percentage >= 60) return 'bg-yellow-400 text-gray-900';
    if (percentage >= 40) return 'bg-orange-500 text-white';
    if (percentage >= 20) return 'bg-red-400 text-white';
    return 'bg-red-600 text-white';
  };

  const getLegendColor = (range: string) => {
    switch (range) {
      case '100%': return 'bg-green-500';
      case '80-99%': return 'bg-lime-400';
      case '60-79%': return 'bg-yellow-400';
      case '40-59%': return 'bg-orange-500';
      case '20-39%': return 'bg-red-400';
      case '<20%': return 'bg-red-600';
      default: return 'bg-gray-400';
    }
  };



  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-center">
              Question-wise Score Heat Map
            </DialogTitle>
            <p className="text-center text-gray-600">
              Performance monitoring for {criteriaName} - {department.charAt(0).toUpperCase() + department.slice(1)} Department
            </p>
          </DialogHeader>

        {/* Performance Legend */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-semibold mb-3">Performance Legend</h3>
          <div className="flex flex-wrap gap-4 text-sm">
            {[
              { range: '100%', label: '100% green' },
              { range: '80-99%', label: '80-99%' },
              { range: '60-79%', label: '60-79%' },
              { range: '40-59%', label: '40-59%' },
              { range: '20-39%', label: '20-39%' },
              { range: '<20%', label: '<20%' }
            ].map((item) => (
              <div key={item.range} className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded ${getLegendColor(item.range)}`}></div>
                <span>{item.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Sub-criteria sections with Accordion */}
        <Accordion type="multiple" className="space-y-4">
          {subCriteria.map((subCrit, index) => (
            <AccordionItem key={subCrit.id} value={subCrit.id} className="border rounded-lg">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center justify-between w-full p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-blue-900">{subCrit.name}</span>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                {(() => {
                  // Get department-specific questions
                  const departmentQuestions = department === 'sales'
                    ? (subCrit.salesQuestions || subCrit.questions || [])
                    : (subCrit.serviceQuestions || subCrit.questions || []);

                  console.log(`Questions for ${subCrit.name} (${department}):`, departmentQuestions);

                  return (
                    <div className="grid gap-3" style={{ gridTemplateColumns: `repeat(${Math.min(departmentQuestions.length, 10)}, minmax(100px, 1fr))` }}>
                      {departmentQuestions.map((question) => {
                        // Safe percentage calculation with null checks
                        const actualScore = question.actualScore || 0;
                        const maxScore = question.maxScore || 1; // Avoid division by zero
                        const percentage = maxScore > 0 ? (actualScore / maxScore) * 100 : 0;
                        const hasIssues = percentage < 60;

                        // Ensure percentage is a valid number
                        const displayPercentage = isNaN(percentage) ? 0 : Math.round(percentage);

                        return (
                          <div key={question.id} className="flex flex-col items-center">
                            <div
                              className={`w-16 h-16 rounded flex flex-col items-center justify-center text-xs font-bold mb-2 cursor-pointer hover:scale-105 transition-transform ${
                                getScoreColor(actualScore, maxScore)
                              }`}
                              title={`Click to view details\n${question.question}\nResponse: ${question.response || 'No Response'}\nScore: ${actualScore}/${maxScore}`}
                              onClick={() => handleQuestionClick(question, subCrit.name)}
                            >
                              <div>{question.id}</div>
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-medium">{displayPercentage}%</div>
                              <div className="text-xs text-gray-600 truncate max-w-[80px]">
                                {question.response || 'No Response'}
                              </div>
                              {hasIssues && (
                                <div className="text-xs text-red-600">
                                  {percentage < 20 ? '2 issues' : '1 issue'}
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  );
                })()}

              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
        </DialogContent>
      </Dialog>

      <QuestionDetailPopup
        isOpen={!!selectedQuestion}
        onClose={handleQuestionPopupClose}
        question={selectedQuestion}
        subCriteriaName={selectedSubCriteriaName}
      />
    </>
  );
};

export default QuestionWiseHeatmap;
